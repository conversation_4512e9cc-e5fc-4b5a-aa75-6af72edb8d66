import { useCallback } from 'react'
import { findOverlayStoryboard, getOverlayTrackIndex } from '@rve/editor/utils/overlay-helper.ts'
import { useMap } from 'usehooks-ts'
import { Overlay, OverlayType, TrackType } from '@app/rve-shared/types'
import { useEditorContext } from '../editor.context'

type ReadonlyMap<K, V> = Omit<Map<K, V>, 'set' | 'delete' | 'clear'>

export type TimelineOverlayActivation = {
  /**
   * 分镜视频轨道的激活状态
   * Key: 分镜序号
   * Value: 该分镜上激活的视频轨道序号
   */
  videoActivation: ReadonlyMap<number, number>

  /**
   * 分镜口播轨道的激活状态
   * Key: 分镜序号
   * Value: 该分镜上激活的口播轨道序号
   */
  narratorActivation: ReadonlyMap<number, number>

  /**
   * 通过点击 Overlay , 将其所在的轨道设置其所在分镜的激活轨道
   */
  toggleOverlaySelection: (overlay: Overlay) => void

  /**
   * 判断指定的 Overlay 是否处于激活状态. 当前仅对分镜视频轨道生效, 其他的默认返回 `true`
   */
  getOverlayActivationState: (overlay: Overlay) => boolean
}

export const useTimelineOverlayActivation = (): TimelineOverlayActivation => {
  const { tracks } = useEditorContext()

  const [videoActivation, videoActivationActions] = useMap<number, number>(
    tracks.find(t => t.type === TrackType.STORYBOARD)
      ?.overlays
      .map((_, index) => {
        // TODO: 查找第一个存在的 Overlay, 而不是默认返回 1
        // const overlays = findOverlaysAboveStorybook(tracks, storyboard)
        return [index, 1]
      })
    || []
  )

  const [narratorActivation, /*narratorActivationActions*/] = useMap<number, number>()

  const toggleOverlaySelection = useCallback<TimelineOverlayActivation['toggleOverlaySelection']>(
    overlay => {
      const storyboard = findOverlayStoryboard(tracks, overlay)
      const trackIndex = getOverlayTrackIndex(tracks, overlay.id)

      if (overlay.type === OverlayType.VIDEO && storyboard && trackIndex !== -1) {
        videoActivationActions.set(storyboard.index, trackIndex)
      }
    },
    [tracks]
  )

  const getOverlayActivationState = useCallback<TimelineOverlayActivation['getOverlayActivationState']>(
    overlay => {
      if (overlay.type !== OverlayType.VIDEO) return true

      const storyboard = findOverlayStoryboard(tracks, overlay)
      const trackIndex = getOverlayTrackIndex(tracks, overlay.id)

      if (!storyboard) return false
      return videoActivation.get(storyboard.index) === trackIndex
    },
    [videoActivation, tracks]
  )

  return {
    videoActivation,
    narratorActivation,
    toggleOverlaySelection,
    getOverlayActivationState,
  }
}

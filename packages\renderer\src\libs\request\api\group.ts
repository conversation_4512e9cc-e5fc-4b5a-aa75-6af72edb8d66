import { fetchPaginationGet, requestCurrying } from '../request'

export type Group = {
  contactMobile?: string
  contactName: string
  contactUserId: number
  id: number
  name: string
  status: number
}

export type Role = {
  code: string
  createTime: number
  dataScope: number
  dataScopeDeptIds: string
  id: number
  name: string
  remark: string
  sort: number
  status: number
  type: number
}

export type Member = {
  avatar: string
  createTime: number
  id: number
  isComment: boolean
  isCreator: boolean
  isPrivateMsg: boolean
  isPublishVideo: boolean
  isTiktokComment: boolean
  memberId: number
  mobile: string
  nickname: string
  remark?: string
}

type InviteParams = {
  roleIds: number[]
  projectIds: number[]
}

export const GroupAPI = {
  list: requestCurrying.get<object, Group[]>('/app-api/creative/team/list'),
  current: requestCurrying.get<object, Group>('/app-api/creative/team/current'),
  check: requestCurrying.post<{ teamId: number }, boolean>('/app-api/creative/team/change'),
  create: requestCurrying.post<{ name: string }, null>('/app-api/creative/team/create'),
  leave: requestCurrying.post<{ teamId: number }, null>('/app-api/creative/team/leave'),
  transfer: requestCurrying.post('/app-api/creative/team/transfer-ownership'),
  invite: requestCurrying.post<InviteParams, string>('/app-api/creative/team-invite/create-code'),
  join: requestCurrying.post<{ inviteCode: string }, null>('/app-api/creative/team-member/join'),
  roles: requestCurrying.get<object, Role[]>('/app-api/creative/team-role/list'),
  members: fetchPaginationGet<Member>('/app-api/creative/team-member/page'),
}

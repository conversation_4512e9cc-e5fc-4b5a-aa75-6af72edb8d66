/**
 * TimelineGrid Component
 * Renders a grid-based timeline view for managing overlay items across multiple rows.
 * Supports drag and drop, resizing, and various item management operations.
 */

import React, { useCallback } from 'react'
import { SNAPPING_CONFIG } from '../../constants'
import { TimelineTrack } from './timeline-track'
import { useEditorContext, useTimeline } from '@rve/editor/contexts'
import {
  closestCenter,
  DndContext,
  DragMoveEvent,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors
} from '@dnd-kit/core'

/**
 * TimelineGrid component that displays overlay items in a row-based timeline view
 */
export const TimelineGrid: React.FC = () => {
  const { tracks, durationInFrames } = useEditorContext()

  const {
    isDragging, alignmentLines, timelineGridRef,
    layout: { totalHeight, rowGap },
    handleOverlayDragStart, handleOverlayDragMove, handleOverlayDragEnd
  } = useTimeline()

  const sensors = useSensors(useSensor(PointerSensor, {
    activationConstraint: {
      distance: 4,
    }
  }))

  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event
    if (active.data.current?.type !== 'timeline-item') return

    const { overlay } = active.data.current as any
    return handleOverlayDragStart(overlay, 'move')
  }, [handleOverlayDragStart])

  const handleDragMove = useCallback((event: DragMoveEvent) => {
    const { x: deltaX } = event.delta

    const targetTrackIndex = event.over?.data.current?.trackIndex

    return handleOverlayDragMove(deltaX, targetTrackIndex)
  }, [handleOverlayDragMove])

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragMove={handleDragMove}
      onDragEnd={handleOverlayDragEnd}
    >
      <div
        ref={timelineGridRef}
        id="TimelineGrid"
        className="relative overflow-y-hidden bg-white dark:bg-gray-900 h-full"
        style={{
          height: totalHeight,
          scrollbarWidth: 'none',
        }}
      >
        {/* Container for Rows and Alignment Lines */}
        <div
          className="absolute inset-0 flex flex-col py-2"
          style={{ rowGap }}
        >
          {/* Render Alignment Lines - Conditionally visible and higher contrast */}
          {isDragging
            && SNAPPING_CONFIG.enableVerticalSnapping
            && alignmentLines.map(frame => (
              <div
                key={`align-${frame}`}
                className="absolute top-0 bottom-0 w-px border-r border-dashed border-gray-500 dark:border-gray-200 z-40 pointer-events-none"
                style={{
                  left: `${(frame / durationInFrames) * 100}%`,
                  height: '100%',
                }}
                aria-hidden="true"
              />
            ))}

          {/* Render Tracks */}
          {tracks.map((track, trackIndex) => (
            <TimelineTrack
              key={trackIndex}
              trackIndex={trackIndex}
              {...track}
            />
          ))}
        </div>
      </div>
    </DndContext>
  )
}

import React, { memo, useCallback } from 'react'

import { OverlayType, TextOverlay } from '@app/rve-shared/types'

import { useOverlayHelper } from '@rve/editor/hooks/helpers/useOverlayHelper.ts'

import { FontStyleResource } from '@/types/resources.ts'
import { useFontManager } from '../../hooks/useFontManager.ts'
import { VIDEO_WIDTH, VIDEO_HEIGHT } from '@rve/editor/constants'

import FontStyleSelector from '../overlays/text/font-style-selector'
import { EnhancedTextRenderer } from '../overlays/text/enhanced-layer-text-renderer'

const TextPluginPanel: React.FC = () => {
  const { addOverlayToGlobalTrack } = useOverlayHelper()
  const { calculateTextSize } = useFontManager()

  // 获取本地字体的 HTTP 访问路径
  const getLocalFontPath = useCallback(() => {
    // 在开发环境中使用绝对路径，在生产环境中使用相对路径
    const isDev = import.meta.env.DEV
    const fontPath = isDev ? '/fonts/shuheiti.ttf' : './fonts/shuheiti.ttf'
    console.log('[字体路径] 环境:', isDev ? '开发' : '生产', '路径:', fontPath)
    return fontPath
  }, [])

  // 创建默认文字覆盖层
  const createDefaultTextOverlay = useCallback(async () => {
    try {
      console.log('[默认文字] 开始创建默认文字覆盖层')

      const defaultText = '默认文字'
      const defaultFontSize = 150
      const localFontPath = getLocalFontPath()

      // 验证字体文件是否可以通过 HTTP 访问
      console.log('[默认文字] 字体文件路径:', localFontPath)

      // 测试字体文件是否可访问
      try {
        const response = await fetch(localFontPath)
        if (!response.ok) {
          throw new Error(`字体文件访问失败: ${response.status} ${response.statusText}`)
        }
        console.log('[默认文字] 字体文件可以正常访问')
      } catch (fetchError) {
        console.error('[默认文字] 字体文件访问测试失败:', fetchError)
        // 继续执行，但可能会有问题
      }

      let calculatedWidth = VIDEO_WIDTH / 4
      let calculatedHeight = VIDEO_HEIGHT / 4

      // 尝试计算文字尺寸
      try {
        const textSize = await calculateTextSize(defaultText, defaultFontSize, localFontPath, '书黑体')
        if (textSize) {
          calculatedWidth = textSize.width
          calculatedHeight = textSize.height
        }
      } catch (sizeError) {
        console.warn('[默认文字] 文字尺寸计算失败，使用默认尺寸:', sizeError)
      }

      const defaultOverlay: TextOverlay = {
        id: Date.now(),
        type: OverlayType.TEXT,
        src: localFontPath, // 使用本地字体文件的 HTTP 路径
        content: defaultText,
        left: 100,
        top: 100,
        width: calculatedWidth,
        height: calculatedHeight,
        durationInFrames: 90,
        from: 0,
        rotation: 0,
        isDragging: false,
        styles: {
          fontSize: defaultFontSize,
          fontWeight: 'normal' as const,
          color: '#ffffff',
          fontFamily: '书黑体', // 使用本地字体名称
          fontStyle: 'normal' as const,
          underlineEnabled: false,
          textAlign: 'center' as const,
          backgroundColor: 'transparent',
          zIndex: 20,
          // 轮廓样式 - 默认关闭
          strokeEnabled: false,
          strokeWidth: 0,
          strokeColor: '#000000',
          // 阴影样式 - 默认关闭
          shadowEnabled: false,
          shadowDistance: 0,
          shadowAngle: 45,
          shadowBlur: 2,
          shadowColor: '#000000',
          shadowOpacity: 0.5,
          // 其他样式
          backgroundImage: undefined,
          bubbleTextRect: undefined,
        }
      }

      console.log('[默认文字] 创建的覆盖层配置:', {
        src: defaultOverlay.src,
        fontFamily: defaultOverlay.styles.fontFamily,
        content: defaultOverlay.content
      })

      addOverlayToGlobalTrack(defaultOverlay)

      console.log('[默认文字] 默认文字覆盖层创建成功')
    } catch (error) {
      console.error('[默认文字] 创建默认文字覆盖层失败:', error)
    }
  }, [addOverlayToGlobalTrack, calculateTextSize, getLocalFontPath])

  const handleAddFontStyleOverlay = useCallback(
    async (fontStyleResource: FontStyleResource.FontStyle, previewOverlay: TextOverlay) => {
      try {
        console.log('[花体字选择] 开始创建花体字覆盖层:', fontStyleResource.content.fontName)

        // 计算文字的实际尺寸
        const defaultText = '默认文字'
        const defaultFontSize = 150
        const fontPath = fontStyleResource.content.fontPath
        const fontName = fontStyleResource.content.fontName

        let calculatedWidth = VIDEO_WIDTH / 4
        let calculatedHeight = VIDEO_HEIGHT / 4

        try {
          const textSize = await calculateTextSize(defaultText, defaultFontSize, fontPath, fontName)
          if (textSize) {
            calculatedWidth = textSize.width
            calculatedHeight = textSize.height
          }
        } catch (sizeError) {
          console.warn('[花体字选择] 文字尺寸计算失败，使用默认尺寸:', sizeError)
        }

        // 使用预览覆盖层作为基础，但更新尺寸和位置
        const finalOverlay: TextOverlay = {
          ...previewOverlay,
          id: fontStyleResource.id,
          type: OverlayType.TEXT,
          content: defaultText,
          left: 100,
          top: 100,
          width: calculatedWidth,
          height: calculatedHeight,
          durationInFrames: 90,
          from: 0,
          rotation: 0,
          isDragging: false,
          styles: {
            ...previewOverlay.styles,
            fontSize: defaultFontSize,
          }
        }

        addOverlayToGlobalTrack(finalOverlay)

        console.log('[花体字选择] 花体字覆盖层创建成功')
      } catch (error) {
        console.error('[花体字选择] 创建花体字覆盖层失败:', error)
      }
    },
    [addOverlayToGlobalTrack, calculateTextSize]
  )

  // 渲染默认文字预设项
  const renderDefaultTextItem = () => {
    const defaultPreviewOverlay: TextOverlay = {
      id: 0,
      type: OverlayType.TEXT,
      src: getLocalFontPath(),
      content: '默认',
      left: 0,
      top: 0,
      width: 80,
      height: 80,
      durationInFrames: 90,
      from: 0,
      rotation: 0,
      isDragging: false,
      styles: {
        fontSize: 28,
        fontWeight: 'normal' as const,
        color: '#ffffff',
        fontFamily: '书黑体',
        fontStyle: 'normal' as const,
        underlineEnabled: false,
        textAlign: 'center' as const,
        backgroundColor: 'transparent',
        zIndex: 20,
        strokeEnabled: false,
        strokeWidth: 0,
        strokeColor: '#000000',
        shadowEnabled: false,
        shadowDistance: 0,
        shadowAngle: 45,
        shadowBlur: 2,
        shadowColor: '#000000',
        shadowOpacity: 0.5,
        backgroundImage: undefined,
        bubbleTextRect: undefined,
      }
    }

    const containerStyle: React.CSSProperties = {
      width: '100%',
      height: '100%',
    }

    return (
      <div
        key="default-text"
        onClick={createDefaultTextOverlay}
        className="group relative overflow-hidden border bg-gray-200 dark:bg-background rounded border-white/10 transition-all dark:hover:border-white/20 hover:border-blue-500/80 cursor-pointer aspect-square w-20"
      >
        <div className="h-full w-full flex items-center justify-center rounded">
          <div className="text-base transform-gpu transition-transform group-hover:scale-102 dark:text-white text-gray-900/90 size-full flex justify-center items-center">
            <EnhancedTextRenderer
              overlay={defaultPreviewOverlay}
              containerStyle={containerStyle}
              isPreview={true}
            />
          </div>
        </div>

        {/* Label */}
        <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-1 truncate">
          默认文字
        </div>
      </div>
    )
  }

  return (
    <div className="h-full">
      <div className="flex flex-wrap gap-3 p-2">
        {/* 默认文字预设 */}
        {renderDefaultTextItem()}
      </div>

      {/* 花体字选择器 */}
      <FontStyleSelector
        onFontStyleSelect={handleAddFontStyleOverlay}
        className="h-full"
      />
    </div>
  )
}

export default memo(TextPluginPanel)

import React, {
  useCallback,
  useState,
  useRef,
  useEffect,
} from 'react'
import { AssetLoadingContext, AssetLoadingContextType } from './context'

interface AssetLoadingProviderProps {
  children: React.ReactNode
}

export const AssetLoadingProvider: React.FC<AssetLoadingProviderProps> = ({
  children,
}) => {
  const [isLoadingAssets, setIsLoadingAssets] = useState(true)
  const [isInitialLoad, setIsInitialLoad] = useState(true)
  const loadingAssetsRef = useRef(new Set<number>())
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const hasInitializedRef = useRef(false)

  const setInitialLoadComplete = useCallback(() => {
    if (hasInitializedRef.current) return
    hasInitializedRef.current = true
    setIsInitialLoad(false)
  }, [])

  const handleAssetLoadingChange = useCallback(
    (overlayId: number, isLoading: boolean) => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current)
      }

      if (isLoading) {
        loadingAssetsRef.current.add(overlayId)
        setIsLoadingAssets(true)
      }
      else {
        loadingAssetsRef.current.delete(overlayId)
        // 添加小延迟以防止闪烁
        loadingTimeoutRef.current = setTimeout(() => {
          if (loadingAssetsRef.current.size === 0) {
            setIsLoadingAssets(false)
          }
        }, 300)
      }
    },
    [],
  )

  // 组件卸载时清理超时
  useEffect(() => {
    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current)
      }
    }
  }, [])

  return (
    <AssetLoadingContext.Provider
      value={{
        isLoadingAssets,
        isInitialLoad,
        handleAssetLoadingChange,
        setInitialLoadComplete,
      }}
    >
      {children}
    </AssetLoadingContext.Provider>
  )
}

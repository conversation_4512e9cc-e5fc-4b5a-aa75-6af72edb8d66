import React, { useState } from 'react'
import { AudioResourceItem } from './audio-resource-item'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { Music, Volume2, Headphones } from 'lucide-react'

/**
 * 使用示例：展示重构后的 AudioResourceItem 组件
 * 现在使用原生 HTML5 音频 + AudioProgressBar 组件
 */
export function AudioResourceItemExample() {
  const [selectedAudio, setSelectedAudio] = useState<string | null>(null)

  // 示例音频数据
  const audioItems = [
    {
      id: 'audio-1',
      title: '背景音乐 - 轻松愉快',
      description: '3:45',
      audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      thumbnailUrl: 'https://via.placeholder.com/150x150/4F46E5/FFFFFF?text=Music',
      resourceType: ResourceType.MUSIC,
      resourceUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      durationMsec: 225000,
      icon: <Music className="w-8 h-8" />
    },
    {
      id: 'audio-2',
      title: '音效 - 点击声',
      description: '0:02',
      audioUrl: 'https://www.soundjay.com/misc/sounds/click-1.wav',
      resourceType: ResourceType.SOUND,
      resourceUrl: 'https://www.soundjay.com/misc/sounds/click-1.wav',
      durationMsec: 2000,
      icon: <Volume2 className="w-8 h-8" />
    },
    {
      id: 'audio-3',
      title: '播客 - 技术分享',
      description: '15:30',
      audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      thumbnailUrl: 'https://via.placeholder.com/150x150/10B981/FFFFFF?text=Podcast',
      resourceType: ResourceType.AUDIO,
      resourceUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      durationMsec: 930000,
      icon: <Headphones className="w-8 h-8" />
    }
  ]

  const handleAudioSelect = (audioId: string) => {
    setSelectedAudio(audioId)
    console.log('选中音频:', audioId)
  }

  const handleAudioAdd = (audioId: string) => {
    console.log('添加音频到项目:', audioId)
  }

  return (
    <div className="p-6 bg-gray-900 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">
          重构后的音频资源项组件 - 使用 AudioProgressBar
        </h1>
        
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-300 mb-4">
            重构完成的功能特性：
          </h2>
          <ul className="text-gray-400 space-y-2 list-disc list-inside">
            <li>✅ 原生 HTML5 音频播放（替代了之前的音频库）</li>
            <li>✅ 集成了封装好的 AudioProgressBar 组件</li>
            <li>✅ 支持点击和拖拽定位的交互式进度条</li>
            <li>✅ 精准寻轨功能</li>
            <li>✅ 播放状态同步</li>
            <li>✅ 重新设计的 NativeAudioManager 架构</li>
            <li>✅ 独立音频实例控制</li>
            <li>✅ 移除了根元素的 onDragStart 事件以防止冲突</li>
          </ul>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {audioItems.map((item) => (
            <div key={item.id} className="relative">
              <AudioResourceItem
                id={item.id}
                title={item.title}
                description={item.description}
                audioUrl={item.audioUrl}
                thumbnailUrl={item.thumbnailUrl}
                icon={item.icon}
                resourceType={item.resourceType}
                resourceUrl={item.resourceUrl}
                durationMsec={item.durationMsec}
                className={`transition-all duration-200 ${
                  selectedAudio === item.id 
                    ? 'ring-2 ring-blue-500 ring-offset-2 ring-offset-gray-900' 
                    : ''
                }`}
                onClick={() => handleAudioSelect(item.id)}
                onAdd={() => handleAudioAdd(item.id)}
                showCollectionButton={true}
                interactInfo={{ collected: false, collectedCount: 0, refCount: 0 }}
              />
              
              {selectedAudio === item.id && (
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full" />
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="mt-8 p-4 bg-gray-800 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-2">使用说明：</h3>
          <div className="text-gray-300 space-y-2">
            <p>• <strong>点击播放按钮</strong>：开始/暂停音频播放</p>
            <p>• <strong>拖拽进度条</strong>：使用 AudioProgressBar 组件进行精确定位</p>
            <p>• <strong>点击进度条</strong>：快速跳转到指定位置</p>
            <p>• <strong>拖拽整个项目</strong>：将音频添加到时间轴（已移除根元素冲突）</p>
            <p>• <strong>悬停效果</strong>：显示播放控制按钮和进度条</p>
          </div>
        </div>

        {selectedAudio && (
          <div className="mt-4 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
            <p className="text-blue-300">
              当前选中: <strong>{audioItems.find(item => item.id === selectedAudio)?.title}</strong>
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default AudioResourceItemExample

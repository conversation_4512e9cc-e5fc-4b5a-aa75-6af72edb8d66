
import * as React from 'react'
import * as RadioGroupPrimitive from '@radix-ui/react-radio-group'
import { cn } from '@/components/lib/utils'
import { CommonCategory } from '@/types/resources'
import { ChevronDownIcon, ChevronUpIcon } from 'lucide-react'
import { CommonDefaultCategory } from '../templates/sticker-templates/constants'

export interface CategorySelectorProps {
  options: CommonCategory[]
  value?: string | number
  onValueChange?: (value: string) => void
  defaultValue?: string
  className?: string
  itemClassName?: string
  activeItemClassName?: string
  disabled?: boolean
}

export function CategorySelector({
  options,
  value,
  onValueChange,
  defaultValue,
  className,
  itemClassName,
  activeItemClassName,
  disabled = false,
  ...props
}: CategorySelectorProps) {
  const [expanded, setExpanded] = React.useState(false)
  
  const toggleExpand = (e: React.MouseEvent) => {
    e.preventDefault()
    setExpanded(!expanded)
  }

  const baseClassname = 'px-2 py-1 rounded text-xs border transition-all cursor-pointer dark:hover:bg-gray-800' +
            'data-[state=checked]:bg-primary data-[state=checked]:hover:bg-muted/30 data-[state=checked]:text-blue-400 data-[state=checked]:border-primary' +
            'disabled:opacity-50 disabled:cursor-not-allowed'
  
  return (
    <div className="relative">
      <RadioGroupPrimitive.Root
        data-slot="category-selector"
        className={cn(
          'flex flex-wrap gap-2 relative',
          expanded ? 'h-auto' : 'h-9 overflow-hidden',
          className
        )}
        value={value?.toString()}
        onValueChange={onValueChange}
        defaultValue={defaultValue}
        disabled={disabled}
        {...props}
      >
        
        {
          CommonDefaultCategory.map(option => (
            <RadioGroupPrimitive.Item
              key={option.id}
              value={option.id.toString()}
              data-slot="category-item"
              className={cn(
                baseClassname,
                itemClassName
              )}
            >
              {option.name}
            </RadioGroupPrimitive.Item>
          ))
        }
        {options.map(option => (
          <RadioGroupPrimitive.Item
            key={option.id}
            value={option.id.toString()}
            data-slot="category-item"
            className={cn(
              baseClassname,
              itemClassName
            )}
          >
            {option.name}
          </RadioGroupPrimitive.Item>
        ))}
      </RadioGroupPrimitive.Root>
      
      {options.length > 5 && (
        <button
          onClick={toggleExpand}
          className="absolute right-0 top-0 h-9 px-2 bg-gradient-to-l from-white via-white dark:from-gray-900 dark:via-gray-900 flex items-center justify-center text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
        >
          {expanded ? (
            <ChevronUpIcon className="w-4 h-4" />
          ) : (
            <ChevronDownIcon className="w-4 h-4" />
          )}
        </button>
      )}
    </div>
  )
} 


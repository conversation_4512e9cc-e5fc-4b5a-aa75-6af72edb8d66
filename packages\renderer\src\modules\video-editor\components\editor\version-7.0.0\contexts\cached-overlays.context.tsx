import React, { PropsWithChildren, useCallback, useContext, useEffect, useState } from 'react'
import {
  CaptionOverlay,
  NarrationOverlay,
  Overlay,
  OverlayType,
  RenderableOverlay,
  SoundOverlay,
  StickerOverlay,
  StoryboardOverlay,
  TextOverlay,
  VideoOverlay
} from '@app/rve-shared/types'
import { useEditorContext } from '@rve/editor/contexts'
import { merge } from 'lodash'
import { useCalculateRenderableOverlays } from '@rve/editor/hooks/useCalculateRenderableOverlays.ts'

type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type OverlayUpdater<T> = DeepPartial<T> | ((overlay: T) => T)

export type GetTypedOverlay<T extends OverlayType> = T extends OverlayType.TEXT
  ? TextOverlay
  : T extends OverlayType.VIDEO
    ? VideoOverlay
    : T extends OverlayType.SOUND
      ? SoundOverlay
      : T extends OverlayType.CAPTION
        ? CaptionOverlay
        : T extends OverlayType.STICKER
          ? StickerOverlay
          : T extends OverlayType.STORYBOARD
            ? StoryboardOverlay
            : T extends OverlayType.NARRATION
              ? NarrationOverlay
              : never

type CachedOverlaysContextValues = {
  overlays: RenderableOverlay[],

  requestUpdate<TOverlayType extends OverlayType>(
    id: Overlay['id'],
    overlay: OverlayUpdater<GetTypedOverlay<TOverlayType>>,
    commit?: boolean
  ): void
}

const CachedOverlaysContext = React.createContext<CachedOverlaysContextValues>(null as any)

export const useCachedOverlaysContext = () => useContext(CachedOverlaysContext)

export const CachedOverlaysProvider: React.FC<PropsWithChildren> = ({
  children
}) => {
  const { updateOverlay } = useEditorContext()
  const calculateFinalOverlays = useCalculateRenderableOverlays()

  const [localOverlays, setLocalOverlays] = useState<RenderableOverlay[]>(() => calculateFinalOverlays())

  useEffect(() => {
    setLocalOverlays(calculateFinalOverlays())
  }, [calculateFinalOverlays])

  const handleUpdateOverlay = useCallback<CachedOverlaysContextValues['requestUpdate']>(
    (id, updater, commit = false) => {
      const index = localOverlays.findIndex(o => o.id === id)
      if (index === -1) return localOverlays
      const target = localOverlays[index]!

      const updatedOverlay = (
        typeof updater === 'function'
          ? updater(target as any)
          : merge({}, target, updater)
      ) as RenderableOverlay

      if (commit) {
        updateOverlay(updatedOverlay.id, () => updatedOverlay)
      }

      setLocalOverlays([
        ...localOverlays.slice(0, index),
        updatedOverlay,
        ...localOverlays.slice(index + 1)
      ])
    },
    [localOverlays]
  )

  return (
    <CachedOverlaysContext.Provider
      value={{
        overlays: localOverlays,
        requestUpdate: handleUpdateOverlay
      }}
    >
      {children}
    </CachedOverlaysContext.Provider>
  )
}

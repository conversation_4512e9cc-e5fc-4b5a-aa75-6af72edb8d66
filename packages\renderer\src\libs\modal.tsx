import React, {
  createContext,
  useContext,
  useCallback,
  ReactNode,
  useState,
  useId,
  useRef,
} from 'react'
import * as DIALOG from '@/components/ui/dialog'
import * as SHEET from '@/components/ui/sheet'

type ModalVariant = 'dialog' | 'sheet'

interface ModalLib {
  Modal: typeof DIALOG.Dialog
  ModalContent: typeof DIALOG.DialogContent
}

const modals: Record<string, ModalLib> = {
  dialog: {
    Modal: DIALOG.Dialog,
    ModalContent: DIALOG.DialogContent,
  },
  sheet: {
    Modal: SHEET.Sheet as any,
    ModalContent: SHEET.SheetContent as any,
  },
}

type ModalHandler = (params: ModalParams) => void

interface ModalParams {
  variant?: ModalVariant
  content: ReactNode
}

type ModalInstance = ModalParams

interface ModalInternalContext {
  open: (id: string, params: ModalParams) => void
}

interface ModalContext {
  close: () => void
  closed: boolean
  variant: ModalVariant
}

const ModalInternalContext = createContext<ModalInternalContext | null>(null)
const ModalContext = createContext<ModalContext | null>(null)

function ModalWrapper({ instance, done }: { instance: ModalInstance; done: () => void }) {
  const { content, variant = 'dialog' } = instance
  const { Modal, ModalContent } = modals[variant]
  const [open, setOpen] = useState(true)

  const close = useCallback(() => {
    setOpen(false)
  }, [])

  const context: ModalContext = {
    variant,
    close,
    closed: !open,
  }

  return (
    <Modal open={open} onOpenChange={isOpen => !isOpen && close()}>
      <ModalContent
        className="!max-w-none min-w-[400px] w-auto flex flex-col"
        onCloseAutoFocus={done}
      >
        <ModalContext.Provider value={context}>{content}</ModalContext.Provider>
      </ModalContent>
    </Modal>
  )
}

export function ModalProvider({ children }: { children: ReactNode }) {
  const [instances, setInstances] = useState<Record<string, ModalInstance>>({})

  const open = useCallback((id: string, params: ModalParams) => {
    setInstances(prev => ({ ...prev, [id]: { ...prev[id], ...params } }))
  }, [])

  const done = useCallback((id: string) => {
    setInstances(prev => (delete prev[id], { ...prev }))
  }, [])

  return (
    <ModalInternalContext.Provider value={{ open }}>
      {children}
      {Object.entries(instances).map(([id, instance]) => (
        <ModalWrapper key={id} instance={instance} done={() => done(id)} />
      ))}
    </ModalInternalContext.Provider>
  )
}

export function useModal(): ModalHandler {
  const id = useId()
  const ctx = useContext(ModalInternalContext)
  const counter = useRef(0)

  if (!ctx) {
    throw new Error('useModal must be used within a ModalProvider')
  }

  const { open } = ctx

  return useCallback(
    params => {
      counter.current++
      open(`${id}${counter.current}`, params)
    },
    [open, id],
  )
}

export function useModalContext() {
  const ctx = useContext(ModalContext)

  if (!ctx) {
    throw new Error('useModalContext must be used in ModalContent')
  }

  return ctx
}

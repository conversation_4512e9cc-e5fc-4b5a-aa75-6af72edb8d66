import React from 'react'
import { CircularProgress } from '@/components/ui/circular-progress'
import { cn } from '@/components/lib/utils'
import { useMixcutContext } from '@/contexts/mixcut.context.tsx'

/**
 * 批量上传进度遮罩组件
 */
export function BatchUploadOverlay() {
  const { generation: { batchUploadState: { visible, completed, total } } } = useMixcutContext()

  if (!visible) return null

  const progress = total > 0 ? (completed / total) * 100 : 0

  return (
    <div
      className={cn(
        'fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm',
      )}
    >
      <div className="bg-background rounded-lg p-8 shadow-lg border max-w-sm w-full mx-4">
        <div className="flex flex-col items-center space-y-6">
          {/* 圆环进度条 */}
          <CircularProgress
            value={progress}
            size={120}
            strokeWidth={8}
            showPercentage={false}
            color="oklch(0.715 0.143 215.221)"
            backgroundColor="oklch(0.551 0.027 264.364)"
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">
                {completed}
              </div>
              <div className="text-sm text-muted-foreground">
                / {total}
              </div>
            </div>
          </CircularProgress>

          {/* 状态文本 */}
          <div className="text-center space-y-2">
            <h3 className="text-lg font-medium text-foreground">
              正在保存混剪结果
            </h3>
          </div>
        </div>
      </div>
    </div>
  )
}

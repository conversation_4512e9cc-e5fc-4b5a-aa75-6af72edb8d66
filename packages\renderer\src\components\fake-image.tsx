import React, { useEffect, useState } from 'react'

function bufferToUint32(buf: <PERSON><PERSON>yBuffer, offset: number): number {
  return new DataView(buf).getUint32(offset, false)
}

async function getTwoHuesFromId(id: string): Promise<[number, number]> {
  const encoder = new TextEncoder()
  const data = encoder.encode(id)
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)

  const h1 = bufferToUint32(hashBuffer, 0) % 360
  const h2raw = bufferToUint32(hashBuffer, 4) % 360
  const h2 = (h2raw + (Math.abs(h1 - h2raw) < 60 ? 90 : 0)) % 360 // 强制分离至少 60°
  return [h1, h2]
}

type Props = {
  id: string
  className?: string
  style?: React.CSSProperties
}

export const FakeImage: React.FC<Props> = ({ id, className = '', style }) => {
  const [gradient, setGradient] = useState<string>('')

  useEffect(() => {
    getTwoHuesFromId(id).then(([h1, h2]) => {
      const g = `linear-gradient(to right, hsl(${h1}, 70%, 60%), hsl(${h2}, 75%, 65%))`
      setGradient(g)
    })
  }, [id])

  return (
    <div
      className={`w-full h-full ${className}`}
      style={{
        backgroundImage: gradient,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        ...style,
      }}
    />
  )
}

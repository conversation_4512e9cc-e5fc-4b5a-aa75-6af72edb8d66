import React, { useMemo } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { Loader2Icon, PlusIcon } from 'lucide-react'
import { cn } from '@/components/lib/utils'
import { useQueryResourceCacheStatus, useQueryResourceLoading } from '@/hooks/queries/useQueryResourceCacheStatus'
import { useResource } from '@rve/editor/hooks/resource/useResource.tsx'

export interface ResourceCacheIndicatorProps {
  /**
   * 资源类型
   */
  resourceType: ResourceType
  /**
   * 资源URL
   */
  resourceUrl: string

  /**
   * 是否正在加载
   */
  isLoading?: boolean
  /**
   * 图标大小
   */
  size?: number
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 点击下载按钮的回调
   */
  onDownload?: () => void
  version?: string

}

/**
 * 资源缓存状态指示器
 * 显示资源是否已缓存到本地
 */
export function ResourceCacheIndicator({
  resourceType,
  resourceUrl,
  isLoading: externalLoading,
  size = 16,
  className,
  version,
  onDownload,
}: ResourceCacheIndicatorProps) {
  const { downloadResourceToCache, setResourceLoadingState } = useResource()

  const { data: isCached, isLoading: checking, refetch } = useQueryResourceCacheStatus(resourceType, resourceUrl)

  const { data: isResourceLoading } = useQueryResourceLoading(resourceType, resourceUrl)

  const isLoading = useMemo(() => {
    return Boolean(externalLoading || isResourceLoading)
  }, [externalLoading, isResourceLoading])

  const handleDownload = async (e: React.MouseEvent) => {
    e.stopPropagation()

    if (onDownload) {
      onDownload()
    } else {
      try {
        setResourceLoadingState(resourceType, resourceUrl, true)

        // 下载资源，为音乐资源添加 customExt 参数
        await downloadResourceToCache({
          url: resourceUrl,
          resourceType,
          version: version || '1.0.0',
          // 如果是音乐资源，添加 mp3 扩展名
          customExt: resourceType === ResourceType.MUSIC ? 'mp3' : undefined
        })
        refetch()
      } catch (error) {
        console.error('下载资源失败:', error)
        // 确保在出错时也重置加载状态
        setResourceLoadingState(resourceType, resourceUrl, false)
      }
    }
  }

  return (
    <div
      className={cn(
        'flex items-center justify-center bg-gray-900/80 rounded p-1 cursor-pointer text-gray-500 hover:bg-gray-900/50 transition-all',
        className
      )}
    >
      {checking ? (
        <Loader2Icon
          className="animate-spin text-gray-400 cursor-pointer"
          style={{ width: size, height: size }}
        />
      ) : isLoading ? (
        <Loader2Icon
          className="animate-spin text-blue-500 cursor-pointer"
          style={{ width: size, height: size }}
        />
      ) : isCached ? (
        <button
          onClick={handleDownload}
        >
          <PlusIcon
            className="hover:text-green-400 text-green-500 cursor-pointer"
            style={{ width: size, height: size }}
          />
        </button>

      ) : (
        <button
          onClick={handleDownload}
        >
          <PlusIcon
            className="text-gray-400 hover:text-blue-500 cursor-pointer"
            style={{ width: size, height: size }}
          />
        </button>
      )}
    </div>
  )
}

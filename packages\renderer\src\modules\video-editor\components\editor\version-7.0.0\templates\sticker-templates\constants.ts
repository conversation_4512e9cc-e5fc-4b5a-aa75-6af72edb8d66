import { ResourceTab } from '../../components/overlays/common/resource-panel-layout'
import { CommonCategory } from '@/types/resources'

export enum ResourceTabType {
  ONLINE = 'online',
  LOCAL = 'local',
  RANK = 'rank'
}

export const CommonResourceTabs: ResourceTab[] = [
  {
    label: '在线',
    value: ResourceTabType.ONLINE,
    showSearch: true,
    showCategorySelector: true,
    searchPlaceholder: '搜索资源，按回车键搜索',
  },
  {
    label: '本地',
    value: ResourceTabType.LOCAL,
    showSearch: true,
    showCategorySelector: false,
    searchPlaceholder: '搜索本地资源，按回车键搜索',
  },
]

export const SoundResourceTabs: ResourceTab[] = [
  {
    label: '音效库',
    value: ResourceTabType.ONLINE,
    showSearch: true,
    showCategorySelector: true,
    showDurationFilter: true,
    searchPlaceholder: '搜索音效，按回车键搜索',
  },
  {
    label: '我的音效',
    value: ResourceTabType.LOCAL,
    showSearch: true,
    showCategorySelector: false,
    showDurationFilter: true,
    searchPlaceholder: '搜索本地音效，按回车键搜索',
  },
]

export const MusicResourceTabs: ResourceTab[] = [
  {
    label: '音乐库',
    value: ResourceTabType.ONLINE,
    showSearch: true,
    showCategorySelector: true,
    showDurationFilter: true,
    searchPlaceholder: '搜索音乐，按回车键搜索',
  },
  {
    label: '热门排行榜',
    value: ResourceTabType.RANK,
    showSearch: false,
    showCategorySelector: false,
    showDurationFilter: true,
  },
  {
    label: '我的音乐',
    value: ResourceTabType.LOCAL,
    showSearch: true,
    showCategorySelector: false,
    showDurationFilter: true,
    searchPlaceholder: '搜索本地音乐，按回车键搜索',
  },
]
export const StickerResourceTabs: ResourceTab[] = [
  {
    label: '贴纸库',
    value: ResourceTabType.ONLINE,
    showSearch: true,
    showCategorySelector: true,
    searchPlaceholder: '搜索贴纸，按回车键搜索',
  },
  {
    label: '我的贴纸',
    value: ResourceTabType.LOCAL,
    showSearch: true,
    showCategorySelector: false,
    searchPlaceholder: '搜索本地贴纸，按回车键搜索',
  },
]

export enum CommonDefaultCategoryEnum {
  ALL= 'all',
  COLLECTED= 'collected',
}

export const CommonDefaultCategory: CommonCategory[] = [
  {
    id: CommonDefaultCategoryEnum.ALL,
    name: '全部',
  },
  {
    id: CommonDefaultCategoryEnum.COLLECTED,
    name: '收藏',
  },
]

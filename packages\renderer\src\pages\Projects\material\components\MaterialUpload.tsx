import React, { useState } from 'react'
import { cn } from '@/components/lib/utils'
import { FileUploader, FileUploaderRenderProps } from '@/components/ui/file-uploader'
import { FolderUploader } from '@/components/ui/folder-uploader'
import { FolderPlus, FolderUp } from 'lucide-react'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { useQueryClient } from '@tanstack/react-query'

interface UploaderCardProps {
  currentFolderId: string
  orientation: 'horizontal' | 'vertical'
}
const CustomUploader: React.FC<FileUploaderRenderProps> = ({
  getRootProps,
  getInputProps,
  isLoading,
}) => {
  return (
    <div className="flex items-center justify-center gap-2">
      <FolderUp className="w-5 h-5" />
      <div {...getRootProps()}>
        <input {...getInputProps()} />
        {isLoading ? '上传中...' : '点击上传'}
      </div>
    </div>
  )
}
const UploaderCard = ({
  currentFolderId,
  orientation = 'horizontal',
}: UploaderCardProps) => {
  const [hovered, setHovered] = useState(false)
  const queryClient = useQueryClient()

  return (
    <div
      className={cn(
        'relative flex items-center justify-center border-dashed border-2 border-gray-200 rounded-lg text-sm cursor-pointer overflow-hidden transition-all duration-300',
        orientation === 'horizontal' ? 'w-50 h-50' : 'w-40 h-64',
      )}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      <div
        className={cn(
          'flex flex-col items-center justify-center transition-opacity duration-300',
          hovered ? 'opacity-0 scale-95' : 'opacity-100 scale-100',
        )}
      >
        <span className="text-3xl text-gray-400 mb-2">+</span>
        <span className="text-gray-500">上传素材</span>
      </div>
      <div
        className={cn(
          'absolute inset-0 text-gray-500 flex flex-col transition-all duration-300',
          hovered ? 'opacity-100 scale-y-100' : 'opacity-0 scale-y-0 pointer-events-none',
          'origin-center',
        )}
      >
        <div className="flex items-center justify-center gap-2 flex-1 hover:text-primary-highlight1 transition-colors">
          <FileUploader
            folderUuid={currentFolderId}
            renderCustomComponent={props => <CustomUploader {...props} />}
          />
        </div>

        <button className="flex items-center justify-center gap-2 flex-1 hover:text-primary-highlight1 transition-colors">
          <FolderUploader
            folderUuid={currentFolderId}
            children={
              <div className="flex items-center justify-center">
                <FolderPlus className="w-5 h-5 mr-2" />
                文件夹上传
              </div>
            }
            isShowUploadedFiles={false}
            showFileList={false}
            onProgress={(current, total) => {
              console.log({ current, total })
            }}
            onUpload={async () => {
              await queryClient.invalidateQueries({
                queryKey: [QUERY_KEYS.MATERIAL_DIRECTORY_LIST],
              })
              await queryClient.invalidateQueries({
                queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST],
              })
            }}
          />
        </button>
      </div>
      <div
        className={cn(
          'absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full border-b-2 border-dashed border-gray-200',
          hovered ? 'opacity-100' : 'opacity-0',
        )}
      />
    </div>
  )
}
export default UploaderCard
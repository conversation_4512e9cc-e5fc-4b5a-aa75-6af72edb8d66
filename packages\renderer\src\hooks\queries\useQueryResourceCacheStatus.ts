import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { useResource } from '@rve/editor/hooks/resource/useResource.tsx'
import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { queryClient } from '@/main'

/**
 * 查询资源缓存状态的 hook
 * @param resourceType 资源类型
 * @param resourceUrl 资源URL
 * @returns 缓存状态的查询结果
 */
export const useQueryResourceCacheStatus = (resourceType?: ResourceType, resourceUrl?: string) => {
  const { isResourceCached } = useResource()

  return useQuery({
    queryKey: [QUERY_KEYS.RESOURCE_CACHE_STATUS, resourceType, resourceUrl],
    queryFn: async () => {
      if (!resourceType || !resourceUrl) return false

      // 首先尝试从 react-query 的缓存中获取资源状态
      const cachedData = queryClient.getQueryData([QUERY_KEYS.RESOURCE_CACHE]) as Record<ResourceType, Record<string, string>> | undefined

      if (cachedData?.[resourceType]?.[resourceUrl]) {
        // 如果缓存中有记录，直接返回 true，避免 IPC 调用
        return true
      }

      // 如果缓存中没有记录，则通过 isResourceCached 向主进程查询
      return isResourceCached(resourceType, resourceUrl)
    },
    staleTime: 60000, // 1分钟内不重新获取，减少 IPC 调用
    refetchInterval: 300000, // 5分钟刷新一次
    refetchOnMount: false, // 组件挂载时不重新获取，减少 IPC 调用
    refetchOnWindowFocus: false, // 窗口获得焦点时不刷新，减少 IPC 调用
    enabled: !!resourceType && !!resourceUrl,
  })
}

/**
 * 查询资源加载状态的 hook
 * @param resourceType 资源类型
 * @param resourceUrl 资源URL
 * @returns 加载状态的查询结果
 */
export const useQueryResourceLoading = (resourceType?: ResourceType, resourceUrl?: string) => {
  const { isResourceLoading } = useResource()

  return useQuery({
    queryKey: [QUERY_KEYS.RESOURCE_LOADING, resourceType, resourceUrl],
    queryFn: () => {
      if (!resourceType || !resourceUrl) return false
      return isResourceLoading(resourceType, resourceUrl)
    },
    // 不缓存加载状态，始终获取最新状态
    staleTime: 0,
    // 不自动重新获取，依赖手动触发
    refetchInterval: false,
    // 禁用自动刷新，避免循环
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: !!resourceType && !!resourceUrl,
  })
}

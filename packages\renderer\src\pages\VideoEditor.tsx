import { SidebarProvider } from '@/components/ui/sidebar'
import ReactVideoEditor from '@/modules/video-editor/react-video-editor.tsx'
import React from 'react'
import { useVirtualTab } from '@/contexts/virtual-tab.context.tsx'

const VideoEditor: React.FC = () => {
  const { params } = useVirtualTab() || {}
  // const [search] = useSearchParams()

  const scriptId = params?.scriptId
  const projectId = params?.projectId

  if (!scriptId) return null

  return (
    <SidebarProvider id="SidebarProvider">
      <ReactVideoEditor scriptId={scriptId} projectId={projectId} />
    </SidebarProvider>
  )
}

export default VideoEditor

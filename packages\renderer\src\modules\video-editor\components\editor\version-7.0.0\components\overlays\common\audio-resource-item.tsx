import React, { ReactNode, useState, useEffect, useCallback } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { ResourceCacheIndicator } from '@/components/ResourceCacheIndicator'
import { useQueryResourceCacheStatus } from '@/hooks/queries/useQueryResourceCacheStatus'
import { InteractInfo } from '@/types/resources'
import { useResource } from '@rve/editor/hooks/resource/useResource.tsx'
import { cn } from '@/components/lib/utils'
import { ResourceCollectionIndicator } from '@/components/ResourceCollectionIndicator'
import { ImageOff } from 'lucide-react'
import { AudioProgressBar } from './audio-progress-bar'

// 音频实例接口
interface AudioInstance {
  id: string | number
  audio: HTMLAudioElement
  isPlaying: boolean
  duration: number
  currentTime: number
  onTimeUpdate?: (currentTime: number, duration: number) => void
  onEnded?: () => void
  onError?: () => void
}

// 重新设计的音频管理器
class EnhancedAudioManager {

  private instances = new Map<string | number, AudioInstance>()
  private currentPlayingId: string | number | null = null

  // 创建音频实例
  createInstance(
    id: string | number,
    audioUrl: string,
    callbacks?: {
      onTimeUpdate?: (currentTime: number, duration: number) => void
      onEnded?: () => void
      onError?: () => void
    }
  ): AudioInstance {
    // 如果实例已存在，先清理
    if (this.instances.has(id)) {
      this.destroyInstance(id)
    }

    const audio = new Audio()
    audio.preload = 'metadata'
    audio.src = audioUrl

    const instance: AudioInstance = {
      id,
      audio,
      isPlaying: false,
      duration: 0,
      currentTime: 0,
      ...callbacks
    }

    // 设置事件监听器
    audio.addEventListener('loadedmetadata', () => {
      instance.duration = audio.duration
    })

    audio.addEventListener('timeupdate', () => {
      instance.currentTime = audio.currentTime
      instance.onTimeUpdate?.(audio.currentTime, audio.duration)
    })

    audio.addEventListener('ended', () => {
      instance.isPlaying = false
      if (this.currentPlayingId === id) {
        this.currentPlayingId = null
      }
      instance.onEnded?.()
    })

    audio.addEventListener('error', () => {
      instance.isPlaying = false
      if (this.currentPlayingId === id) {
        this.currentPlayingId = null
      }
      instance.onError?.()
    })

    this.instances.set(id, instance)
    return instance
  }

  // 播放音频
  async play(id: string | number): Promise<void> {
    const instance = this.instances.get(id)
    if (!instance) return

    // 停止当前播放的音频
    if (this.currentPlayingId && this.currentPlayingId !== id) {
      await this.pause(this.currentPlayingId)
    }

    try {
      await instance.audio.play()
      instance.isPlaying = true
      this.currentPlayingId = id

      // 发送全局事件
      const event = new CustomEvent('audiomanager:play', { detail: { id } })
      window.dispatchEvent(event)
    } catch (error) {
      console.error('播放音频失败:', error)
      instance.onError?.()
    }
  }

  // 暂停音频
  async pause(id: string | number): Promise<void> {
    const instance = this.instances.get(id)
    if (!instance) return

    instance.audio.pause()
    instance.isPlaying = false

    if (this.currentPlayingId === id) {
      this.currentPlayingId = null
    }

    // 发送全局事件
    const event = new CustomEvent('audiomanager:pause', { detail: { id } })
    window.dispatchEvent(event)
  }

  // 切换播放状态
  async toggle(id: string | number): Promise<void> {
    const instance = this.instances.get(id)
    if (!instance) return

    if (instance.isPlaying) {
      await this.pause(id)
    } else {
      await this.play(id)
    }
  }

  // 设置播放位置（寻轨）
  seek(id: string | number, time: number): void {
    const instance = this.instances.get(id)
    if (!instance) return

    // 如果音频还没有加载完成，等待加载
    if (!instance.audio.duration) {
      // 监听loadedmetadata事件，加载完成后再寻轨
      const handleLoadedMetadata = () => {
        const clampedTime = Math.max(0, Math.min(time, instance.audio.duration))
        instance.audio.currentTime = clampedTime
        instance.currentTime = clampedTime
        instance.audio.removeEventListener('loadedmetadata', handleLoadedMetadata)

        // 发送寻轨事件
        const event = new CustomEvent('audiomanager:seek', { detail: { id, time: clampedTime } })
        window.dispatchEvent(event)
      }
      instance.audio.addEventListener('loadedmetadata', handleLoadedMetadata)
      return
    }

    // 确保时间在有效范围内
    const clampedTime = Math.max(0, Math.min(time, instance.audio.duration))
    instance.audio.currentTime = clampedTime
    instance.currentTime = clampedTime

    // 发送寻轨事件
    const event = new CustomEvent('audiomanager:seek', { detail: { id, time: clampedTime } })
    window.dispatchEvent(event)
  }

  // 获取实例信息
  getInstance(id: string | number): AudioInstance | undefined {
    return this.instances.get(id)
  }

  // 获取当前播放的实例ID
  getCurrentPlayingId(): string | number | null {
    return this.currentPlayingId
  }

  // 销毁音频实例
  destroyInstance(id: string | number): void {
    const instance = this.instances.get(id)
    if (!instance) return

    instance.audio.pause()
    instance.audio.src = ''
    instance.audio.load()

    if (this.currentPlayingId === id) {
      this.currentPlayingId = null
    }

    this.instances.delete(id)
  }

  // 销毁所有实例
  destroyAll(): void {
    for (const id of this.instances.keys()) {
      this.destroyInstance(id)
    }
  }
}

// 全局音频管理器实例
const AudioManager = new EnhancedAudioManager()

export interface AudioResourceItemProps {
  /**
   * 资源ID
   */
  id: string | number
  /**
   * 资源标题
   */
  title: string
  /**
   * 资源描述或副标题
   */
  description?: string | number
  /**
   * 资源缩略图URL
   */
  thumbnailUrl?: string
  /**
   * 默认图标 (Lucide图标)
   */
  icon?: ReactNode
  /**
   * 音频URL
   */
  audioUrl?: string
  /**
   * 是否正在加载
   */
  isLoading?: boolean
  /**
   * 点击添加按钮的回调
   */
  onAdd?: () => void
  /**
   * 点击整个资源项的回调
   */
  onClick?: () => void

  /**
   * 自定义内容
   */
  children?: ReactNode
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 资源类型，用于检查本地缓存
   */
  resourceType?: ResourceType
  /**
   * 资源URL，用于检查本地缓存
   */
  resourceUrl?: string
  /**
   * 资源时长（毫秒）
   */
  durationMsec?: number
  /**
   * 自定义扩展名
   */
  customExt?: string,

  /**
   * 是否显示收藏按钮
   */
  showCollectionButton?: boolean
  /**
   * 交互信息，包含收藏状态
   */
  interactInfo?: InteractInfo
  /**
   * 收藏状态变更回调
   */
  onCollectionChange?: (collected: boolean) => void
}

/**
 * 音频资源项组件
 * 用于展示音频资源列表中的单个资源项，支持播放和显示进度
 */
export function AudioResourceItem({
  id,
  title,
  thumbnailUrl,
  icon,
  audioUrl,
  isLoading = false,
  onAdd,
  onClick,
  children,
  className = '',
  resourceType,
  resourceUrl,
  description,
  durationMsec,
  showCollectionButton = true,
  interactInfo,
  onCollectionChange,
  customExt,
}: AudioResourceItemProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [audioInitialized, setAudioInitialized] = useState(false)
  const [imageLoadError, setImageLoadError] = useState(false)

  const isCollected = interactInfo?.collected || false

  const { data: isCached = false } = useQueryResourceCacheStatus(resourceType, resourceUrl)
  const { getResourcePathSync } = useResource()

  // 当缩略图 URL 改变时重置图片加载错误状态
  useEffect(() => {
    setImageLoadError(false)
  }, [thumbnailUrl])

  // 音频事件回调
  const handleTimeUpdate = useCallback((currentTime: number, duration: number) => {
    setCurrentTime(currentTime)
    setDuration(duration)
  }, [])

  const handleAudioEnded = useCallback(() => {
    setIsPlaying(false)
    setCurrentTime(0)
  }, [])

  const handleAudioError = useCallback(() => {
    setIsPlaying(false)
    console.error('音频播放出错')
  }, [])

  // 监听全局音频事件
  useEffect(() => {
    const handleGlobalPlay = (event: Event) => {
      const customEvent = event as CustomEvent
      if (customEvent.detail && customEvent.detail.id === id) {
        setIsPlaying(true)
      } else if (customEvent.detail && customEvent.detail.id !== id) {
        setIsPlaying(false)
      }
    }

    const handleGlobalPause = (event: Event) => {
      const customEvent = event as CustomEvent
      if (customEvent.detail && customEvent.detail.id === id) {
        setIsPlaying(false)
      }
    }

    const handleGlobalSeek = (event: Event) => {
      const customEvent = event as CustomEvent
      if (customEvent.detail && customEvent.detail.id === id) {
        setCurrentTime(customEvent.detail.time)
      }
    }

    window.addEventListener('audiomanager:play', handleGlobalPlay)
    window.addEventListener('audiomanager:pause', handleGlobalPause)
    window.addEventListener('audiomanager:seek', handleGlobalSeek)

    return () => {
      window.removeEventListener('audiomanager:play', handleGlobalPlay)
      window.removeEventListener('audiomanager:pause', handleGlobalPause)
      window.removeEventListener('audiomanager:seek', handleGlobalSeek)
    }
  }, [id])

  const collectionIndicator = resourceType && id && showCollectionButton && (
    <div className={cn(
      'absolute right-0 top-0 transition-opacity duration-200',
      isCollected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
    )}
    >
      <ResourceCollectionIndicator
        resourceType={resourceType}
        resourceId={id}
        isCollected={isCollected}
        size={12}
        onCollectionChange={onCollectionChange}
      />
    </div>
  )

  // 初始化音频实例
  const initializeAudio = useCallback(() => {
    if (audioInitialized || !audioUrl) return

    // 确定音频URL
    let finalAudioUrl = audioUrl
    if (isCached && resourceType && resourceUrl) {
      const localPath = getResourcePathSync(resourceType, resourceUrl)
      if (localPath) {
        finalAudioUrl = localPath
      }
    }

    // 创建音频实例
    AudioManager.createInstance(id, finalAudioUrl, {
      onTimeUpdate: handleTimeUpdate,
      onEnded: handleAudioEnded,
      onError: handleAudioError
    })

    setAudioInitialized(true)
  }, [audioUrl, id, isCached, resourceType, resourceUrl, getResourcePathSync, audioInitialized, handleTimeUpdate, handleAudioEnded, handleAudioError])

  // 切换播放状态
  const togglePlay = useCallback(async (e: React.MouseEvent) => {
    e.stopPropagation()

    // 如果音频尚未初始化，先初始化
    if (!audioInitialized) {
      initializeAudio()
      // 等待一小段时间让音频初始化完成
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    // 切换播放状态
    await AudioManager.toggle(id)
  }, [audioInitialized, initializeAudio, id])

  // 处理进度条寻轨
  const handleSeek = useCallback((time: number) => {
    if (!audioInitialized) {
      initializeAudio()
      // 等待初始化完成后再寻轨
      setTimeout(() => {
        AudioManager.seek(id, time)
      }, 100)
    } else {
      AudioManager.seek(id, time)
    }

    // 立即更新本地状态，提供即时反馈
    setCurrentTime(time)
  }, [audioInitialized, initializeAudio, id])

  // 处理拖拽开始事件
  const handleDragStart = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    if (!resourceType || !resourceUrl ) return

    // 设置拖拽数据
    const dragData = {
      resourceType,
      resourceUrl,
      id,
      title,
      durationMsec: durationMsec || (duration ? Math.round(duration * 1000) : 10000),
      customExt,
    }

    e.dataTransfer.setData('application/json', JSON.stringify(dragData))
    e.dataTransfer.effectAllowed = 'copy'

    if (thumbnailUrl) {
      const img = new Image()
      img.src = thumbnailUrl
      e.dataTransfer.setDragImage(img, 25, 25)
    }
  }, [resourceType, resourceUrl, id, title, durationMsec, duration, thumbnailUrl, customExt])

  const cacheIndicator = resourceType && resourceUrl && (
    <div className="absolute right-0 bottom-0">
      <ResourceCacheIndicator
        resourceType={resourceType}
        resourceUrl={resourceUrl}
        isLoading={isLoading}
        size={12}
        onDownload={onAdd}
      />
    </div>
  )

  // const playButton = audioUrl && (
  //   <button
  //     onClick={togglePlay}
  //     className="
  //       absolute
  //       top-0
  //       right-0
  //       rounded
  //       cursor-pointer
  //       text-gray-500
  //       hover:text-white
  //       hover:bg-gray-600
  //       p-1
  //       transition-all
  //       z-10
  //       opacity-0 group-hover:opacity-100
  //     "
  //   >
  //     {isPlaying ? <PauseIcon size={16} /> : <PlayIcon size={16} />}
  //   </button>
  // )

  // 处理点击事件，默认为播放功能
  const handleClick = useCallback((e: React.MouseEvent) => {
    if (onClick) {
      // 如果提供了onClick回调，则调用它
      onClick()
    } else if (audioUrl) {
      // 否则默认为播放/暂停功能
      togglePlay(e)
    }
  }, [onClick, audioUrl, togglePlay])

  // 清理音频实例
  useEffect(() => {
    return () => {
      // 销毁音频实例
      AudioManager.destroyInstance(id)
    }
  }, [id])

  return (
    <div
      className={`aspect-square ${className}`}
      onClick={handleClick}
      draggable={!!resourceType && !!resourceUrl }
      onDragStart={handleDragStart}
    >
      <div
        className={cn(
          `group relative w-full h-full
          bg-muted/30
          rounded  dark:bg-gray-800/40
          border  dark:border-gray-700/10
          hover:border-blue-500/20 dark:hover:border-blue-500/20
          hover:bg-blue-500/5 dark:hover:bg-blue-500/5
          transition-all overflow-hidden`,
          isCached ? 'border-green-500/20 dark:border-green-500/20' : '',
          resourceType && resourceUrl ? 'cursor-grab active:cursor-grabbing' : ''
        )}
      >
        {
          description && (
            <div className="text-[10px] border border-gray-500 bg-gray-500/20 rounded px-1 h-4 flex items-center justify-center text-gray-500 absolute bottom-1 left-1 z-10">
              {description}
            </div>
          )
        }

        {thumbnailUrl ? (
          <div className="absolute inset-0 flex items-center justify-center">
            {imageLoadError ? (
              <div className="flex items-center justify-center text-gray-400">
                <ImageOff className="w-8 h-8" />
              </div>
            ) : (
              <img
                src={thumbnailUrl}
                alt={title}
                className="max-w-full max-h-full object-contain"
                loading="lazy"
                onError={() => setImageLoadError(true)}
                onLoad={() => setImageLoadError(false)}
              />
            )}
          </div>
        ) : icon && (
          <div className="absolute inset-0 flex items-center justify-center text-gray-400">
            {icon}
          </div>
        )}

        {/* 音频进度条 */}
        {audioUrl && (isPlaying || currentTime > 0) && (
          <div className="absolute bottom-0 left-0 right-0 px-1 pb-1">
            <AudioProgressBar
              currentTime={currentTime}
              duration={duration}
              isPlaying={isPlaying}
              onSeek={handleSeek}
              height={2}
            />
          </div>
        )}

        {/* {playButton} */}
        {cacheIndicator}
        {collectionIndicator}
      </div>
      {children}
      {
        title && (
          <div className="text-xs w-full text-neutral-300 truncate mt-2">
            {title}
          </div>
        )
      }
    </div>
  )
}

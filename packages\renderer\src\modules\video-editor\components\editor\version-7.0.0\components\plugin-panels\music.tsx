import React, { memo, useCallback, useState, useMemo } from 'react'
import { Overlay, OverlayType } from '@app/rve-shared/types'
import { SoundResource } from '@/types/resources.ts'
import { ResourcePanelLayout } from '../overlays/common/resource-panel-layout.tsx'
import { Music } from 'lucide-react'
import { useEditorContext } from '@rve/editor/contexts/editor.context.tsx'
import { useTimeline } from '@rve/editor/contexts/timeline.context.tsx'
import { useResource } from '../../hooks/resource/useResource.tsx'
import { useResourceLoadingState } from '../../hooks/resource/useResourceLoadingState.tsx'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { InfiniteResourceList } from '@/components/InfiniteResourceList.tsx'
import {  MusicResourceTabs, ResourceTabType } from '../../templates/sticker-templates/constants.ts'
import { AudioResourceItem } from '../overlays/common/audio-resource-item.tsx'
import { useInfiniteQueryMusicRankList, useInfiniteQueryMusicUnified, useQueryMusicCategory } from '@/hooks/queries/useQueryMusic.ts'
import { useOverlayHelper } from '@rve/editor/hooks/helpers/useOverlayHelper.ts'

const MusicItem = memo(({
  item,
  isLoading,
  onItemAdd,
  onCollectionChange,
}: {
  item: SoundResource.Sound
  isLoading: boolean
  onItemClick: () => void
  onItemAdd: () => void
  onCollectionChange?: (collected: boolean) => void
}) => {
  const durationInSeconds = (item.content.durationMsec / 1000).toFixed(1) + 's'

  return (
    <div className="relative">
      <AudioResourceItem
        key={item.id}
        id={item.id}
        title={item.title}
        thumbnailUrl={item.cover?.url || ''}
        icon={<Music className="w-8 h-8" />}
        isLoading={isLoading}
        onAdd={onItemAdd}
        resourceType={ResourceType.MUSIC}
        resourceUrl={item.content.itemUrl}
        description={durationInSeconds}
        audioUrl={item.content.itemUrl}
        durationMsec={item.content.durationMsec}
        customExt="mp3"
        interactInfo={item.interactInfo}
        onCollectionChange={onCollectionChange}
        showCollectionButton={true}
        version={item?.version}
      />
    </div>
  )
})

/**
 * 音效面板组件
 * 显示各种音效，并允许用户将音效添加到时间轴
 */
export function MusicPanel() {
  const { overlays, durationInFrames: compositionDuration } = useEditorContext()
  const { visibleRows } = useTimeline()
  const { downloadResourceToCache, getResourcePathSync } = useResource()
  const { addOverlayToGlobalTrack } = useOverlayHelper()

  const { data: musicCategory } = useQueryMusicCategory()

  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [searchKey, setSearchKey] = useState<string>('')
  const [activeTab, setActiveTab] = useState<string>(ResourceTabType.ONLINE)

  const infiniteQueryResult = useInfiniteQueryMusicUnified({
    pageSize: 50,
    selectedCategory: selectedCategory,
    search: searchKey,
    enabled: activeTab === ResourceTabType.ONLINE
  })

  const infiniteRankQueryResult = useInfiniteQueryMusicRankList({
    pageSize: 50,
    search: searchKey,
    enabled: activeTab === ResourceTabType.RANK
  })

  const handleCategoryChange = useCallback((value: string) => {
    setSelectedCategory(value)
  }, [])

  const handleTabChange = useCallback((value: string) => {
    setActiveTab(value)
  }, [])

  const handleCollectionChange = useCallback((collected: boolean) => {
    console.log('音乐收藏状态变更:', collected)
  }, [])

  const handleAddMusicToTimeline = useCallback(
    async (music: SoundResource.Sound) => {
      try {
        const existingPath = getResourcePathSync(ResourceType.MUSIC, music.content.itemUrl)

        if (!existingPath) {
          await downloadResourceToCache({
            url: music.content.itemUrl,
            resourceType: ResourceType.MUSIC,
            id: music.id,
            customExt: 'mp3'
          })
        }

        const musicDurationInFrames = Math.round(music.content.durationMsec / 1000 * 30)

        const newOverlay: Overlay = {
          id: Date.now(),
          type: OverlayType.SOUND,
          content: music.content.itemUrl,
          src: music.content.itemUrl,
          durationInFrames: musicDurationInFrames,
          from: 0,
          height: 100,
          width: 200,
          left: 0,
          top: 0,
          isDragging: false,
          rotation: 0,
          styles: {
            volume: 1,
          },
        }

        addOverlayToGlobalTrack(newOverlay)
      } catch (error) {
        console.error('添加音效到时间轴失败:', error)
      }
    },
    [
      addOverlayToGlobalTrack,
      overlays,
      visibleRows,
      compositionDuration,
      downloadResourceToCache,
      getResourcePathSync
    ]
  )

  const handleMusicClick = useCallback(async (data: SoundResource.Sound) => {
    try {
      await downloadResourceToCache({
        url: data.content.itemUrl,
        resourceType: ResourceType.MUSIC,
        id: data.id,
        customExt: 'mp3'
      })
      handleAddMusicToTimeline(data)
    } catch (error) {
      console.error('下载音效失败:', error)
    }
  }, [downloadResourceToCache])

  // 音乐项包装组件，处理异步加载状态
  const MusicItemWrapper = useCallback(({ item, index }: { item: SoundResource.Sound, index: number }) => {
    const isLoading = useResourceLoadingState(ResourceType.MUSIC, item.content.itemUrl)

    return (
      <MusicItem
        key={`music-${item.id}-${index}`}
        item={item}
        isLoading={isLoading}
        onItemClick={() => handleAddMusicToTimeline(item)}
        onItemAdd={() => handleMusicClick(item)}
        onCollectionChange={handleCollectionChange}
      />
    )
  }, [handleAddMusicToTimeline, handleMusicClick, handleCollectionChange])

  const renderMusicItem = useCallback((item: SoundResource.Sound, index: number) => {
    return <MusicItemWrapper item={item} index={index} />
  }, [MusicItemWrapper])

  const renderMusicContent = useCallback(() => {
    return (
      <div>
        
        <InfiniteResourceList
          queryResult={infiniteQueryResult}
          renderItem={renderMusicItem}
          emptyText="该分类暂无音效"
          loadingText="加载音效中..."
          itemsContainerClassName="grid grid-cols-3 gap-3 pt-3 pb-3"
        />
      </div>
    )
  }, [infiniteQueryResult, renderMusicItem])

  const renderMusicRankContent = useCallback(() => {
    return (
      <InfiniteResourceList
        queryResult={infiniteRankQueryResult}
        renderItem={renderMusicItem}
        emptyText="该分类暂无音效"
        loadingText="加载音效中..."
        itemsContainerClassName="grid grid-cols-4 gap-3 pt-3 pb-3"
      />
    )
  }, [infiniteRankQueryResult, renderMusicItem])

  const renderLocalStickerContent = useCallback(() => {
    // const exampleResources: LocalResourceItem[] = Array(8).fill(0).map((_, index) => ({
    //   id: `music-${index}`,
    //   type: 'music',
    //   name: `音乐 ${index + 1}`,
    //   path: ''
    // }))

    return (
      <div>
        1
      </div>
      // <LocalResourcePanel
      //   resources={exampleResources}
      //   emptyText="暂无本地音乐"
      //   renderResourceItem={(_resource, index) => (
      //     <div key={index} className="aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
      //       <Music className="w-8 h-8 text-gray-400" />
      //     </div>
      //   )}
      // />
    )
  }, [])

  const hasMusic = useMemo(() => {
    const firstPage = infiniteQueryResult.data?.pages?.[0]
    return !!firstPage && firstPage.list.length > 0
  }, [infiniteQueryResult.data])

  const hasRankMusic = useMemo(() => {
    const firstPage = infiniteRankQueryResult.data?.pages?.[0]
    return !!firstPage && firstPage.list.length > 0
  }, [infiniteRankQueryResult.data])

  // 标签页内容渲染映射 - 优化：避免在每次渲染时创建新函数
  const tabContentRenderers = useMemo(() => ({
    [ResourceTabType.ONLINE]: renderMusicContent,
    [ResourceTabType.RANK]: renderMusicRankContent,
    [ResourceTabType.LOCAL]: renderLocalStickerContent,
  }), [renderMusicContent, renderMusicRankContent, renderLocalStickerContent])

  // 空状态配置映射
  const emptyStateConfig = useMemo(() => ({
    [ResourceTabType.ONLINE]: {
      isEmpty: !hasMusic,
      emptyText: '该分类暂无音乐'
    },
    [ResourceTabType.RANK]: {
      isEmpty: !hasRankMusic,
      emptyText: '暂无热门音乐'
    },
    [ResourceTabType.LOCAL]: {
      isEmpty: false, // 本地资源暂时不检查空状态
      emptyText: '暂无本地音乐'
    },
  }), [hasMusic, hasRankMusic])

  // 配置标签页内容
  const tabsWithContent = useMemo(() => {
    return MusicResourceTabs.map(tab => {
      const renderer = tabContentRenderers[tab.value as keyof typeof tabContentRenderers]
      const emptyState = emptyStateConfig[tab.value as keyof typeof emptyStateConfig]

      return {
        ...tab,
        renderContent: renderer || (() => null),
        isEmpty: emptyState?.isEmpty || false,
        emptyText: emptyState?.emptyText || '暂无数据',
      }
    })
  }, [tabContentRenderers, emptyStateConfig])

  return (
    <ResourcePanelLayout
      tabs={tabsWithContent}
      defaultTab={ResourceTabType.ONLINE}
      categories={musicCategory}
      selectedCategory={selectedCategory}
      onCategoryChange={handleCategoryChange}
      searchKey={searchKey}
      onSearchChange={setSearchKey}
      onTabChange={handleTabChange}
    />
  )
}

export default memo(MusicPanel)

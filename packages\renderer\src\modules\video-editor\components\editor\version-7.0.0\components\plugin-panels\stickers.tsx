import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Overlay, OverlayType } from '@app/rve-shared/types'
import { Player } from '@remotion/player'
import { Sequence } from 'remotion'
import {
  useInfiniteQueryLocalPasterList,
  useInfiniteQueryPasterUnified,
  useQueryPasterCategory,
  useQueryPasterDirList,
} from '@/hooks/queries/useQueryPaster.ts'
import { PasterResource } from '@/types/resources.ts'
import { FPS } from '@rve/editor/constants'
import { ResourcePanelLayout } from '../overlays/common/resource-panel-layout.tsx'
import { ResourceTabType, StickerResourceTabs } from '../../templates/sticker-templates/constants.ts'
import { ResourceItem } from '../overlays/common/resource-item.tsx'
import { StickerIcon } from 'lucide-react'
import { useEditorContext, useTimeline } from '@rve/editor/contexts'
import { useResource } from '../../hooks/resource/useResource.tsx'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { InfiniteResourceList } from '@/components/InfiniteResourceList.tsx'
import LocalResourcePanel from '@/components/LocalResourcePanel.tsx'
import SmartStickerItem from '../overlays/stickers/smart-sticker-item.tsx'
import { useOverlayHelper } from '@rve/editor/hooks/helpers/useOverlayHelper.ts'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys.ts'
import { ResourceModule } from '@/libs/request/api/resource.ts'
import { UploadedFile } from '@/components/ui/file-uploader.tsx'
import LocalStickerItem from '../overlays/stickers/local-sticker-item.tsx'
import { ResourceSource } from '@/types/resources'

// Wrapper component for sticker preview with static frame
const StickerPreview = memo(
  ({ template, onClick }: { template: any; onClick: () => void }) => {
    const playerRef = useRef<any>(null)
    const { Component } = template

    const stickerDuration = template.config.defaultProps?.durationInFrames || 100

    const previewProps = {
      overlay: {
        id: -1,
        type: OverlayType.STICKER,
        content: template.config.id,
        category: template.config.category,
        durationInFrames: stickerDuration,
        from: 0,
        height: 100,
        width: 200,
        left: 0,
        top: 0,
        row: 0,
        isDragging: false,
        rotation: 0,
        styles: {
          opacity: 1,
          ...template.config.defaultProps?.styles,
        },
      },
      isSelected: false,
      ...template.config.defaultProps,
    }

    const MemoizedComponent = memo(Component)

    const PreviewComponent = memo(() => (
      <Sequence from={0} durationInFrames={stickerDuration}>
        <MemoizedComponent {...previewProps} />
      </Sequence>
    ))

    PreviewComponent.displayName = 'PreviewComponent'

    const handleMouseEnter = useCallback(() => {
      if (playerRef.current) {
        playerRef.current.seekTo(0)
        playerRef.current.play()
      }
    }, [])

    const handleMouseLeave = useCallback(() => {
      if (playerRef.current) {
        playerRef.current.pause()
        playerRef.current.seekTo(15)
      }
    }, [])

    return (
      <button
        onClick={onClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={`
          group relative w-full h-full
          rounded-lg bg-gray-100/40 dark:bg-gray-800/40
          border border-gray-800/10 dark:border-gray-700/10
          hover:border-blue-500/20 dark:hover:border-blue-500/20
          hover:bg-blue-500/5 dark:hover:bg-blue-500/5
          transition-all duration-200 overflow-hidden
          ${template.config.isPro ? 'relative' : ''}
        `}
      >
        <div className="absolute inset-0 flex items-center justify-center">
          <Player
            ref={playerRef}
            component={PreviewComponent}
            durationInFrames={stickerDuration}
            compositionWidth={template.config.layout === 'double' ? 280 : 140}
            compositionHeight={140}
            fps={FPS}
            initialFrame={15}
            autoPlay={false}
            loop
            controls={false}
            style={{
              width: template.config.layout === 'double' ? '100%' : '140px',
              height: '140px',
            }}
          />
        </div>
      </button>
    )
  },
  (prevProps, nextProps) => prevProps.template.config.id === nextProps.template.config.id,
)

StickerPreview.displayName = 'StickerPreview'

// 贴纸项组件
const StickerItem = memo(
  ({
    item,
    isLoading,
    onItemClick,
    onItemAdd,
    onCollectionChange,
  }: {
    item: PasterResource.Paster
    isLoading: boolean
    onItemClick: () => void
    onItemAdd: () => void
    onCollectionChange?: (collected: boolean) => void
  }) => (
    <div className="aspect-square">
      <ResourceItem
        key={item.id}
        id={item.id}
        title={item.title}
        thumbnailUrl={item.content.thumbUrl}
        icon={<StickerIcon className="w-8 h-8" />}
        isLoading={isLoading}
        onClick={onItemClick}
        onAdd={onItemAdd}
        resourceType={ResourceType.STICKER}
        resourceUrl={item.content.thumbUrl}
        onCollectionChange={onCollectionChange}
        interactInfo={item.interactInfo}
      />
    </div>
  ),
)

StickerItem.displayName = 'StickerItem'

/**
 * 贴纸面板组件
 * 显示各种贴纸模板，并允许用户将贴纸添加到时间轴
 */
export function StickersPanel() {
  const { overlays, durationInFrames } = useEditorContext()
  const { visibleRows } = useTimeline()
  const { cacheStickerForEditor } = useResource()
  const { addOverlayToGlobalTrack } = useOverlayHelper()
  const queryClient = useQueryClient()
  const { data: pasterCategory } = useQueryPasterCategory()

  const [selectedCategory, setSelectedCategory] = useState<string>('')

  const [searchKey, setSearchKey] = useState<string>('')

  const infiniteQueryResult = useInfiniteQueryPasterUnified({
    pageSize: 30,
    selectedCategory: selectedCategory,
    search: searchKey,
  })
  //本地资源
  const { data: dirList } = useQueryPasterDirList() // 请求本地目录
  const [currentFolderId, setCurrentFolderId] = useState('') //当前目录
  //更新当前目录
  useEffect(() => {
    if (dirList?.length && !currentFolderId) {
      setCurrentFolderId(dirList[0].id)
    }
  }, [dirList, currentFolderId])
  //刷新目录
  const onRefershLocalResource = useCallback(async () => {
    await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LOCAL_PASTER_FOLDER_LIST] })
  }, [queryClient])
  //点击目录回调
  const handleFolderChange = useCallback(
    async (folderId: string) => {
      setCurrentFolderId(folderId)
      await onRefershLocalResource()
    },
    [onRefershLocalResource],
  )

  const handleCategoryChange = useCallback((value: string) => {
    setSelectedCategory(value)
  }, [])

  const handleAddStickerToTimeline = useCallback(
    async (paster: PasterResource.Paster) => {
      try {
        await cacheStickerForEditor(paster)

        const resourceUrl = paster.content.fileUrl

        const newOverlay: Overlay = {
          id: Date.now(),
          type: OverlayType.STICKER,
          content: resourceUrl,
          src: resourceUrl,
          durationInFrames: 50,
          from: 0,
          height: 150,
          width: 150,
          left: 0,
          top: 0,
          isDragging: false,
          rotation: 0,
          styles: {
            opacity: 1,
            zIndex: 1,
          },
        }

        addOverlayToGlobalTrack(newOverlay)
      } catch (error) {
        console.error('添加贴纸到时间轴失败:', error)
      }
    },
    [addOverlayToGlobalTrack, overlays, visibleRows, durationInFrames, cacheStickerForEditor],
  )
  const handleAddStickerToTimelines = useCallback(async () => {
    console.log('添加本地贴纸')
  }, [])

  const handleStickerPreCache = useCallback(
    async (paster: PasterResource.Paster) => {
      try {
        // 预缓存完整资源
        await cacheStickerForEditor(paster)
      } catch (error) {
        console.error('预缓存贴纸失败:', error)
      }
    },
    [cacheStickerForEditor],
  )

  const renderStickerItem = useCallback(
    (item: PasterResource.Paster, index: number) => {
      return (
        <SmartStickerItem
          key={`sticker-${item.id}-${index}`}
          sticker={item}
          onItemClick={() => handleAddStickerToTimeline(item)}
          onItemAdd={() => handleStickerPreCache(item)}
        />
      )
    },
    [handleAddStickerToTimeline, handleStickerPreCache],
  )

  const renderStickerContent = useCallback(() => {
    return (
      <InfiniteResourceList
        queryResult={infiniteQueryResult}
        renderItem={renderStickerItem}
        emptyText="该分类暂无贴纸"
        loadingText="加载贴纸中..."
        itemsContainerClassName="grid grid-cols-4 gap-3 pt-3 pb-3"
      />
    )
  }, [infiniteQueryResult, renderStickerItem])

  const renderLocalStickerContent = useCallback(() => {
    const { data: localResources } = useInfiniteQueryLocalPasterList({
      pageSize: 30,
      folderUuid: currentFolderId,
      search: searchKey,
    })

    //上传文件回调
    const handleUploadComplete = useCallback(async (uploaded: UploadedFile[], folderId: string) => {
      console.log('执行创建')

      // 上传成功的文件数组
      for (const file of uploaded) {
        try {
          await ResourceModule.paster.localCreate({
            folderUuid: folderId,
            title: file.fileName || file.file.name,
            fileMd5: file.fileMd5 ?? '',
            contentType: 'paster',
            objectId: file.objectId ?? '',
          })
          console.log('文件已成功上传并创建！')
        } catch (err) {
          console.error('上传失败：', err)
        }
      }
      // TODO: 上传完成后刷新资源列表
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LOCAL_PASTER_LIST] })
    }, [])
    return (
      <LocalResourcePanel
        dirList={dirList || []}
        currentFolderId={currentFolderId}
        onFolderChange={handleFolderChange}
        resourceType={ResourceSource.LOCAL_STICK}
        resourceFolderType={ResourceSource.LOCAL_STICK_FOLDER}
        fileUploadTypes={['image/*']}
        searchKey={searchKey}
        // 控制 MoveDialog 的参数
        // renameDirectory={renameDirectory}
        // 基本参数
        resources={localResources}
        onUploadComplete={handleUploadComplete}
        emptyText="暂无本地贴纸"
        renderResourceItem={(resource, index) => (
          <LocalStickerItem
            key={`sticker-${resource.id}-${index}`}
            sticker={resource}
            onItemClick={() => handleAddStickerToTimelines()}
          />
        )}
      />
    )
  }, [dirList, currentFolderId, handleFolderChange])

  const hasStickers = useMemo(() => {
    const firstPage = infiniteQueryResult.data?.pages?.[0]
    return !!firstPage && firstPage.list.length > 0
  }, [infiniteQueryResult.data])

  // 配置标签页内容
  const tabsWithContent = useMemo(() => {
    return StickerResourceTabs.map(tab => ({
      ...tab,
      renderContent: () => {
        switch (tab.value) {
          case ResourceTabType.ONLINE:
            return renderStickerContent()
          case ResourceTabType.LOCAL:
            return renderLocalStickerContent()
          default:
            return null
        }
      },
      isEmpty: tab.value === ResourceTabType.ONLINE ? !hasStickers : false,
      emptyText: tab.value === ResourceTabType.ONLINE ? '该分类暂无贴纸' : '暂无本地贴纸',
    }))
  }, [renderStickerContent, renderLocalStickerContent, hasStickers])

  return (
    <ResourcePanelLayout
      tabs={tabsWithContent}
      defaultTab={ResourceTabType.ONLINE}
      categories={pasterCategory}
      selectedCategory={selectedCategory}
      onCategoryChange={handleCategoryChange}
      searchKey={searchKey}
      onSearchChange={setSearchKey}
    />
  )
}

export default memo(StickersPanel)

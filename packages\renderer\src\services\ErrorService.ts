import { AxiosError } from 'axios'
import { toast } from 'react-toastify'
import {
  ErrorType,
  ErrorSeverity,
  ErrorInfo,
  ErrorHandlerOptions,
  ErrorReportData
} from '@/types/error'
import { HTTP_STATUS_MESSAGE, BUSINESS_CODE_MESSAGE } from '@/libs/request/config'

/**
 * 错误处理服务
 * 统一处理各种类型的错误，支持错误提示和上报
 */
class ErrorService {

  // 会话开始时间
  private sessionStartTime: number = Date.now()
  // 错误上报 URL
  private reportUrl: string = import.meta.env.VITE_ERROR_REPORT_URL || ''
  // 是否为开发环境
  private isDev: boolean = import.meta.env.DEV
  // 应用版本
  private appVersion: string = import.meta.env.VITE_APP_VERSION || '0.0.0'
  // 默认错误处理选项
  private defaultOptions: ErrorHandlerOptions = {
    showToast: true,
    report: true,
    severity: ErrorSeverity.ERROR
  }

  /**
   * 创建错误信息对象
   * @param message 错误消息
   * @param code 错误代码
   * @param type 错误类型
   * @param options 错误处理选项
   * @returns 错误信息对象
   */
  public createErrorInfo(
    message: string,
    code: string | number = 'UNKNOWN_ERROR',
    type: ErrorType = ErrorType.UNKNOWN,
    options: ErrorHandlerOptions = {}
  ): ErrorInfo {
    const { severity = ErrorSeverity.ERROR, context } = { ...this.defaultOptions, ...options }

    return {
      message,
      code,
      type,
      severity,
      timestamp: Date.now(),
      context
    }
  }

  /**
   * 处理 HTTP 错误
   * @param error Axios 错误对象
   * @param options 错误处理选项
   * @returns 格式化的错误信息
   */
  public handleHttpError(error: AxiosError, options: ErrorHandlerOptions = {}): ErrorInfo {
    const { response, request, message, code, stack } = error
    const mergedOptions = { ...this.defaultOptions, ...options }

    let errorInfo: ErrorInfo

    // 有响应但状态码错误
    if (response) {
      const status = response.status
      const statusText = response.statusText
      const errorMessage = HTTP_STATUS_MESSAGE[status] || statusText || '请求失败'

      errorInfo = this.createErrorInfo(
        errorMessage,
        status,
        ErrorType.HTTP,
        {
          ...mergedOptions,
          context: {
            url: request?.url,
            method: request?.method,
            data: response.data,
            headers: response.headers,
            ...mergedOptions.context
          }
        }
      )
    }
    // 请求已发送但没有收到响应
    else if (request) {
      errorInfo = this.createErrorInfo(
        '网络错误，服务器未响应',
        'NETWORK_ERROR',
        ErrorType.NETWORK,
        {
          ...mergedOptions,
          context: {
            url: request.url,
            method: request.method,
            ...mergedOptions.context
          }
        }
      )
    }
    // 请求配置错误
    else {
      errorInfo = this.createErrorInfo(
        message || '请求配置错误',
        code || 'REQUEST_ERROR',
        ErrorType.HTTP,
        mergedOptions
      )
    }

    // 添加原始错误和堆栈
    errorInfo.originalError = error
    errorInfo.stack = stack

    // 处理错误（显示提示、上报）
    this.processError(errorInfo, mergedOptions)

    return errorInfo
  }

  /**
   * 处理业务错误
   * @param code 业务错误代码
   * @param options 错误处理选项
   * @returns 格式化的错误信息
   */
  public handleBusinessError(code: number, options: ErrorHandlerOptions = {}): ErrorInfo {
    const mergedOptions = { ...this.defaultOptions, ...options }
    // 优先使用自定义消息，如果没有则使用预定义的消息
    const message = options.customMessage || BUSINESS_CODE_MESSAGE[code] || '未知业务错误'

    const errorInfo = this.createErrorInfo(
      message,
      code,
      ErrorType.BUSINESS,
      mergedOptions
    )

    // 处理错误（显示提示、上报）
    this.processError(errorInfo, mergedOptions)

    return errorInfo
  }

  /**
   * 处理运行时错误
   * @param error 错误对象
   * @param options 错误处理选项
   * @returns 格式化的错误信息
   */
  public handleRuntimeError(error: Error, options: ErrorHandlerOptions = {}): ErrorInfo {
    const mergedOptions = { ...this.defaultOptions, ...options }

    const errorInfo = this.createErrorInfo(
      error.message,
      'RUNTIME_ERROR',
      ErrorType.RUNTIME,
      {
        ...mergedOptions,
        severity: ErrorSeverity.ERROR
      }
    )

    // 添加原始错误和堆栈
    errorInfo.originalError = error
    errorInfo.stack = error.stack

    // 处理错误（显示提示、上报）
    this.processError(errorInfo, mergedOptions)

    return errorInfo
  }

  /**
   * 处理 IPC 错误
   * @param error IPC 错误对象
   * @param options 错误处理选项
   * @returns 格式化的错误信息
   */
  public handleIPCError(error: any, options: ErrorHandlerOptions = {}): ErrorInfo {
    const mergedOptions = { ...this.defaultOptions, ...options }

    const errorInfo = this.createErrorInfo(
      error.message || '未知 IPC 错误',
      error.code || 'IPC_ERROR',
      ErrorType.IPC,
      mergedOptions
    )

    // 添加原始错误和详情
    errorInfo.originalError = error
    errorInfo.context = {
      ...errorInfo.context,
      details: error.details
    }

    // 处理错误（显示提示、上报）
    this.processError(errorInfo, mergedOptions)

    return errorInfo
  }

  /**
   * 处理通用错误
   * @param error 任意错误对象
   * @param options 错误处理选项
   * @returns 格式化的错误信息
   */
  public handleError(error: unknown, options: ErrorHandlerOptions = {}): ErrorInfo {
    // 根据错误类型调用相应的处理方法
    if (error instanceof AxiosError) {
      return this.handleHttpError(error, options)
    }
    else if (error instanceof Error) {
      return this.handleRuntimeError(error, options)
    }
    else if (typeof error === 'object' && error !== null && 'code' in error) {
      // 可能是 IPC 错误或业务错误
      if ('message' in error && typeof error.message === 'string') {
        if ('details' in error) {
          // 可能是 IPC 错误
          return this.handleIPCError(error, options)
        }
        // 可能是业务错误
        return this.handleBusinessError(Number(error.code), options)
      }
    }

    // 处理未知类型的错误
    const mergedOptions = { ...this.defaultOptions, ...options }
    const message = typeof error === 'string' ? error : '发生未知错误'

    const errorInfo = this.createErrorInfo(
      message,
      'UNKNOWN_ERROR',
      ErrorType.UNKNOWN,
      mergedOptions
    )

    errorInfo.originalError = error

    // 处理错误（显示提示、上报）
    this.processError(errorInfo, mergedOptions)

    return errorInfo
  }

  /**
   * 处理错误（显示提示、上报）
   * @param errorInfo 错误信息
   * @param options 错误处理选项
   */
  private processError(errorInfo: ErrorInfo, options: ErrorHandlerOptions = {}): void {
    const { showToast = true, report = true } = { ...this.defaultOptions, ...options }

    // 显示错误提示
    if (showToast) {
      this.showErrorToast(errorInfo)
    }

    // 上报错误
    if (report && this.reportUrl) {
      this.reportError(errorInfo)
    }

    // 开发环境下打印错误信息
    if (this.isDev) {
      console.error(`[${errorInfo.type.toUpperCase()}] ${errorInfo.code}: ${errorInfo.message}`, errorInfo)
    }
  }

  /**
   * 显示错误提示
   * @param errorInfo 错误信息
   */
  private showErrorToast(errorInfo: ErrorInfo): void {
    // 根据错误严重程度设置 Toast 类型
    const toastType = this.mapSeverityToToastType(errorInfo.severity)

    toast(errorInfo.message, {
      type: toastType,

    })
  }

  /**
   * 将错误严重程度映射到 Toast 类型
   * @param severity 错误严重程度
   * @returns Toast 类型
   */
  private mapSeverityToToastType(severity: ErrorSeverity): 'error' | 'warning' | 'info' {
    switch (severity) {
      case ErrorSeverity.FATAL:
      case ErrorSeverity.ERROR:
        return 'error'
      case ErrorSeverity.WARNING:
        return 'warning'
      case ErrorSeverity.INFO:
        return 'info'
      default:
        return 'error'
    }
  }

  /**
   * 上报错误
   * @param errorInfo 错误信息
   */
  private async reportError(errorInfo: ErrorInfo): Promise<void> {
    try {
      // 如果没有设置上报 URL，则不上报
      if (!this.reportUrl) {
        return
      }

      // 准备上报数据（始终包含详细信息）
      const reportData = this.prepareReportData(errorInfo)

      // 发送上报请求
      await fetch(this.reportUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reportData),
        // 使用 keepalive 确保请求在页面卸载时仍能完成
        keepalive: true
      })
    }
    catch (reportError) {
      // 上报失败，仅在开发环境下打印错误
      if (this.isDev) {
        console.error('Error reporting failed:', reportError)
      }
    }
  }

  /**
   * 准备上报数据
   * @param errorInfo 错误信息
   * @returns 上报数据
   */
  private prepareReportData(errorInfo: ErrorInfo): ErrorReportData {
    // 基本上报数据
    const reportData: ErrorReportData = {
      error: {
        message: errorInfo.message,
        code: errorInfo.code,
        type: errorInfo.type,
        severity: errorInfo.severity,
        timestamp: errorInfo.timestamp
      },
      app: {
        version: this.appVersion,
        environment: this.isDev ? 'development' : 'production'
      },
      session: {
        startTime: this.sessionStartTime
      }
    }

    // 添加用户信息
    const userId = localStorage.getItem('userId')
    const username = localStorage.getItem('username')

    if (userId || username) {
      reportData.user = {
        id: userId || undefined,
        username: username || undefined
      }
    }

    // 添加设备信息
    reportData.device = {
      os: navigator.platform,
      browser: navigator.userAgent
    }

    // 添加错误上下文
    if (errorInfo.context) {
      reportData.error.context = errorInfo.context
    }

    // 添加堆栈信息
    if (errorInfo.stack) {
      reportData.error.stack = errorInfo.stack
    }

    return reportData
  }

  /**
   * 全局错误处理器（用于 window.onerror）
   * @param message 错误消息
   * @param source 错误源
   * @param lineno 行号
   * @param colno 列号
   * @param error 错误对象
   */
  public globalErrorHandler = (
    message: string | Event,
    source?: string,
    lineno?: number,
    colno?: number,
    error?: Error
  ): boolean => {
    // 如果有原始错误对象，直接处理它
    if (error) {
      this.handleRuntimeError(error, {
        context: { source, lineno, colno }
      })
    }
    else {
      // 否则创建一个新的错误信息
      const errorMessage = message instanceof Event ? 'Unhandled event error' : message

      this.handleError(errorMessage, {
        context: { source, lineno, colno }
      })
    }

    // 返回 true 表示错误已处理
    return true
  }

  /**
   * 全局未处理的 Promise 拒绝处理器（用于 window.onunhandledrejection）
   * @param event 未处理的 Promise 拒绝事件
   */
  public unhandledRejectionHandler = (event: PromiseRejectionEvent): void => {
    const { reason } = event

    this.handleError(reason, {
      context: { unhandledRejection: true }
    })

    // 阻止默认处理
    event.preventDefault()
  }

  /**
   * 安装全局错误处理器
   */
  public installGlobalHandlers(): void {
    window.onerror = this.globalErrorHandler
    window.onunhandledrejection = this.unhandledRejectionHandler
  }
}

// 创建错误服务单例
export const errorService = new ErrorService()

// 导出单例
export default errorService

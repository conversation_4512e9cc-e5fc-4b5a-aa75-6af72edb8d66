import {
  Form as FormPromitive,
  FormField as Form<PERSON>ieldPrimitive,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { zodResolver } from '@hookform/resolvers/zod'
import React, { FormHTMLAttributes, useCallback, useEffect } from 'react'
import {
  ControllerProps,
  useForm,
  UseFormProps,
  FieldValues,
  Control,
  Path,
  FieldPath,
  UseFormReturn,
  SubmitHandler as UseFormSubmitHandler,
} from 'react-hook-form'
import { z } from 'zod'
import { cn } from '@/components/lib/utils'
import { Skeleton } from '@/components/ui/skeleton'

type Render<T extends FieldValues, U extends FieldPath<T> = FieldPath<T>> = ControllerProps<
  T,
  U
>['render']

interface FormFieldProps<T extends FieldValues, U extends FieldPath<T> = FieldPath<T>>
  extends Omit<ControllerProps<T, U>, 'name' | 'render'> {
  label?: string
  description?: string
  render?: Render<T, U>
}

type FormFieldRecord<T extends FieldValues> = {
  [key in Path<T>]: FormFieldProps<T, key> | Render<T, key>
}

interface FormParams<T extends FieldValues> extends UseFormProps<T> {
  fields?: FormFieldRecord<T>
}

export type SubmitHandler<TFieldValues extends FieldValues> = (
  data: TFieldValues,
  form: UseFormReturn<TFieldValues>,
  event?: React.BaseSyntheticEvent,
) => unknown | Promise<unknown>

interface FormProps<T extends FieldValues>
  extends UseFormProps<T>,
  Omit<FormHTMLAttributes<HTMLFormElement>, 'onSubmit'> {
  onSubmit?: SubmitHandler<T>
  pending?: boolean
}

const defaultRender: Render<any> = ({ field }) => <Input {...field} placeholder="请输入" />

function Field<T extends FieldValues>({
  control,
  name,
  props,
  pending,
}: {
  control: Control<T>
  name: Path<T>
  props?: FormFieldProps<T> | Render<T>
  pending?: boolean
}) {
  const render = typeof props === 'function' ? props : (props?.render ?? defaultRender)
  const params = typeof props === 'function' ? {} : props

  const fieldRender: ControllerProps<T>['render'] = data => (
    <FormItem>
      <FormLabel>{params?.label ?? name}</FormLabel>
      <FormControl>{!pending ? render(data) : <Skeleton className="h-10 border" />}</FormControl>
      {params?.description && <FormDescription>{params.description}</FormDescription>}
      <FormMessage className="h-4" children={' '} />
    </FormItem>
  )

  return (
    <FormFieldPrimitive key={name} {...props} control={control} name={name} render={fieldRender} />
  )
}

export function genForm<T extends z.ZodObject<any>>(schema: T, params?: FormParams<z.infer<T>>) {
  const fields = Object.keys(schema.shape) as Path<T>[]

  return (props: FormProps<z.infer<T>>) => {
    const form = useForm({
      ...params,
      ...props,
      resolver: params?.resolver ?? zodResolver(schema),
    })

    useEffect(() => {
      if (props.pending) return
      form.reset(props.defaultValues)
    }, [props.pending])

    const onSubmit: UseFormSubmitHandler<z.infer<T>> = useCallback(
      (data, event) => {
        if (props.pending) return
        props.onSubmit?.(data, form, event)
      },
      [props.onSubmit],
    )

    const {
      className,
      children,
      onSubmit: _onSubmit,
      defaultValues,
      pending,
      ...formProps
    } = props

    return (
      <FormPromitive {...form}>
        <form
          {...formProps}
          className={cn('flex flex-col gap-2', props.className)}
          onSubmit={form.handleSubmit(onSubmit)}
        >
          {fields.map(name => (
            <Field
              key={name}
              control={form.control}
              name={name}
              props={params?.fields?.[name as any]}
              pending={props.pending}
            />
          ))}
          {props.children}
        </form>
      </FormPromitive>
    )
  }
}
